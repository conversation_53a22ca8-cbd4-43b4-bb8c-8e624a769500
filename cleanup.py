from datetime import datetime
from flask import Flask, jsonify
from clean_img_dir import start_cleanup_threads
from config import PORT

app = Flask(__name__)

@app.route('/get_timestamp_with_ms', methods=['POST'])
def get_timestamp_with_ms():
    now = datetime.now()
    timestamp = now.strftime('%Y%m%d-%H%M%S.') + f'{int(now.microsecond/1000):03d}'
    return jsonify({'timestamp': timestamp})


if __name__ == '__main__':
    # Start cleanup threads
    start_cleanup_threads()
    app.run(host='0.0.0.0', port=PORT)