
curl -X POST http://localhost:9000/zhipu_ai   -H "Content-Type: application/json"   -d '{
     "image_url": [
       "https://ti-aoi-1257195185.cos.ap-guangzhou.myqcloud.com/home/<USER>/adp/27.jpg"
     ]
   }'

 curl -X POST http://localhost:9000/get_timestamp_with_ms   -H "Content-Type: application/json"



curl https://ms-85d9mr7f-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-85d9mr7f/ImageQuality/FakePhoto -d '{
  "url": "https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/wIyLuAuorQWAognNbulo-1961348897926998016.jpg",
  "session_id": "test",
  "app_id": "1234",
  "fake_type": 1
}'


curl  https://ms-2vpzc55c-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-2vpzc55c/Attribute/ObjAttribute  -H 'Content-Type: application/json' -d '{
  "ImageUrl": "https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/eaARoqQDdWPareXHGhbH-1962511932419308736.jpg",
  "SessionID": "test21212121"
}'


curl  https://ms-gcqvhkkt-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-gcqvhkkt/SmokeDetectService/SmokeDetect  -H 'Content-Type: application/json' -d '{
  "url_list": ["https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/YFVWEvzrHdqEVtapilLi-1962349779067134144.jpg"],
  "session_id": "test",
  "draw_image_type_list": [2]
}'

curl  https://ms-qgh6pldh-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-qgh6pldh/trafficapi/RecVehicle  -H 'Content-Type: application/json' -d@plate_ocr.json


curl -X POST https://ms-6zbjgs97-100006623454-sw.gw.ap-shanghai.ti.tencentcs.com/ms-6zbjgs97/fake_photo  -H 'Content-Type: application/json'  -d '{

  "image_url":"https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/wIyLuAuorQWAognNbulo-1961348897926998016.jpg"

}'


curl -X POST https://ms-6zbjgs97-100006623454-sw.gw.ap-shanghai.ti.tencentcs.com/ms-6zbjgs97/smoke_detect  -H 'Content-Type: application/json'  -d '{

    "image_url":"https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/YFVWEvzrHdqEVtapilLi-1962349779067134144.jpg" 
}'


多辆车 https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/mMLQPutiYbTjiTaoMqMY-1962776780519904064.jpeg
单辆车：https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/uCuqrOfNewFMtfTDlREX-1962445579777651456.jpg
不带安全帽 https://lke-realtime-1251316161.cos.ap-guangzhou.myqcloud.com/public/1889238639340683264/1948301447461632704/image/VTtLTQcLnzdSEMhKksDT-1962766383512135296.jpeg

curl -X POST \
  https://open.bigmodel.cn/api/paas/v4/chat/completions \
  -H "Authorization: Bearer a26a34abcb654e4aad5279b158e2d500.BOEMXw83EFP7J2fy" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "glm-4.5v",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "https://ti-aoi-1257195185.cos.ap-guangzhou.myqcloud.com/home/<USER>/adp/27.jpg"
            }
          },
          {
            "type": "text",
            "text": "Where is the second bottle of beer from the right on the table?  Provide coordinates in [[xmin,ymin,xmax,ymax]] format"
          }
        ]
      }
    ],
    "thinking": {
      "type":"enabled"
    }
}'

curl -X POST \
  https://open.bigmodel.cn/api/paas/v4/chat/completions \
  -H "Authorization: Bearer a26a34abcb654e4aad5279b158e2d500.BOEMXw83EFP7J2fy" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "glm-4.5v",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "https://ti-aoi-1257195185.cos.ap-guangzhou.myqcloud.com/home/<USER>/adp/27.jpg"
            }
          },
          {
            "type": "text",
            "text": "描述一下这张图片"
          }
        ]
      }
    ],
    "thinking": {
      "type": "enabled"
    }
}'
