import os
# Configuration from Environment Variables with defaults
RESOURCE_PREFIX = os.getenv('RESOURCE_PREFIX', '/workspace')
HTTPS_PROXY = os.getenv('PROXY', 'http://10.0.192.5:8888')

IMAGE_HTTPS_PREFIX = os.getenv('IMAGE_HTTPS_PREFIX', 'https://ms-6zbjgs97-100006623454-sw.gw.ap-shanghai.ti.tencentcs.com/ms-6zbjgs97')
ALLOWED_DOMAINS = os.getenv('ALLOWED_DOMAINS', 'myqcloud.com').split(',')
PORT = int(os.getenv('PORT', 9000))

DOWNLOAD_DIR = os.path.join(RESOURCE_PREFIX, 'downloaded_img')
VISUAL_DIR = os.path.join(RESOURCE_PREFIX, 'visualizations')
MODEL_DIR = os.path.join(RESOURCE_PREFIX, 'models')

# Read MAX_IMAGE_SIZE and ALLOWED_EXTENSIONS from environment variables
MAX_IMAGE_SIZE = int(os.getenv("MAX_IMAGE_SIZE", 50 * 1024 * 1024))  # Default to 50MB if not set
ALLOWED_EXTENSIONS = os.getenv("ALLOWED_EXTENSIONS", ".jpg,.jpeg,.png,.bmp,.webp").lower().split(',')
DOWNLOAD_TIMEOUT = int(os.getenv("DOWNLOAD_TIMEOUT", 5))  # Default to 5 seconds if not set