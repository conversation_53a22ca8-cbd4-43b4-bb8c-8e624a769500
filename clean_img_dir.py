import os
import glob
import time
from threading import Thread
from config import D<PERSON>NLOAD_DIR, VISUAL_DIR
import traceback

# Max files to keep in each directory
IMG_MAX_FILES = int(os.getenv('IMG_MAX_FILES', '200'))

# Max age in days for files
IMG_MAX_AGE = int(os.getenv('IMG_MAX_AGE', '7'))

CLEAN_PERIOD_SECONDS = int(os.getenv('CLEAN_PERIOD_SECONDS', '3600'))  # Default to 1 hour

def clean_directory(directory, max_files=100, max_age_days=7):
    """
    Clean up directory by:
    - Keeping only the most recent max_files
    - Deleting files older than max_age_days
    """
    while True:
        try:
            # Get all files in directory
            files = glob.glob(os.path.join(directory, '*'))
            
            # Delete by age first
            if max_age_days:
                now = time.time()
                age_seconds = max_age_days * 24 * 60 * 60
                for f in files:
                    if os.stat(f).st_mtime < now - age_seconds:
                        print(f"Deleting old file: {f}")
                        os.remove(f)
            
            # Delete by count if still too many
            files = sorted(glob.glob(os.path.join(directory, '*')), key=os.path.getmtime)
            print(f"Cleaning directory {directory}, current file count: {len(files)}")
            while len(files) > max_files:
                os.remove(files[0])
                files.pop(0)
                
        except Exception as e:
            print(f"Error cleaning directory {directory}: {traceback.format_exc()}")

        # Sleep for 1 hour between cleanups
        time.sleep(CLEAN_PERIOD_SECONDS)

def start_cleanup_threads():
    """Start cleanup threads for download and visual directories"""
    Thread(target=clean_directory, args=(DOWNLOAD_DIR, IMG_MAX_FILES, IMG_MAX_AGE), daemon=True).start()
    Thread(target=clean_directory, args=(VISUAL_DIR, IMG_MAX_FILES, IMG_MAX_AGE), daemon=True).start()