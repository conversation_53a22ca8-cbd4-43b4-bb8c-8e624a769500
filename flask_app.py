import json
import traceback
import uuid
import os
import numpy as np
from urllib.parse import urlparse
from datetime import datetime
import requests
from flask import Flask, request, jsonify, send_from_directory, abort, g
from logconf import get_logger
from config import DOWNLOAD_DIR, HTTPS_PROXY, IMAGE_HTTPS_PREFIX, ALLOWED_DOMAINS, MODEL_DIR, VISUAL_DIR, PORT, MAX_IMAGE_SIZE, ALLOWED_EXTENSIONS, DOWNLOAD_TIMEOUT
import ipaddress
import base64
from typing import List,Dict, Any, Tuple,Optional
from urllib.request import urlopen
from PIL import Image, ImageDraw, ImageFont
import io
import cv2
import time
import sys
import httpx
import random

app = Flask(__name__)
logger = get_logger()

def image_to_base64(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

# 设置UTF-8编码支持
if sys.version_info >= (3, 7):
    sys.stdout.reconfigure(encoding='utf-8')
else:
    sys.stdout = open(sys.stdout.fileno(), mode='w', encoding='utf-8', buffering=1)
        
# 保存Base64图片的函数
def save_base64_images(draw_image_list: List[str],service:str, session_id: str):
    # 创建以session_id命名的子目录
    session_dir = os.path.join(VISUAL_DIR,service,session_id)
    if not os.path.exists(session_dir):
        os.makedirs(session_dir)
    
    image_paths = []
    
    for i, base64_data in enumerate(draw_image_list):
        if not base64_data:
            logger.info(f"警告: 第{i}个图像数据为空，跳过")
            continue
            
        try:
            # 处理可能的Base64数据前缀
            if ',' in base64_data:
                base64_data = base64_data.split(',')[1]
                
            # 解码Base64数据
            image_data = base64.b64decode(base64_data)
            
            # 生成文件名
            filename = f"detected_image_{session_id}_{i+1}.jpg"
            filepath = os.path.join(session_dir, filename)
            
            # 保存图像文件
            with open(filepath, "wb") as f:
                f.write(image_data)
                
            image_paths.append(filepath)
            logger.info(f"已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"处理第{i}个图像时出错: {e}")
    
    return image_paths

def generate_session_id():
    return f"{uuid.uuid4().hex}"

@app.before_request
def assign_session_id():
    # Generate a session ID if it doesn't exist
    if not hasattr(g, 'session_id'):
        g.session_id = str(uuid.uuid4())  #UUID

def download_image_with_timestamp(url, target_dir=DOWNLOAD_DIR, https_proxy=HTTPS_PROXY):
    os.makedirs(target_dir, exist_ok=True)
    
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)
    ext = os.path.splitext(filename)[1].lower() or '.jpg'

    # Validate file extension
    if ext not in ALLOWED_EXTENSIONS:
        error_message = f"Invalid file extension: {ext}. Only {', '.join(ALLOWED_EXTENSIONS)} are allowed."
        logger.error(error_message)
        return "", error_message
    
    # Define allowed ports
    ALLOWED_PORTS = [80, 443, 8080, 8090]
    
    def validate_url(url):
        try:
            result = urlparse(url)
            
            # Validate scheme
            if result.scheme not in ['http', 'https']:
                raise ValueError("Invalid URL scheme: must be http or https")
            
            # Validate port
            if result.port is not None and result.port not in ALLOWED_PORTS:
                raise ValueError(f"Port {result.port} is not allowed. Allowed ports: {ALLOWED_PORTS}")
            
            # Validate hostname is not an IP address (prevents internal network access)
            try:
                ipaddress.ip_address(result.hostname)
                raise ValueError("IP addresses are not allowed")
            except ValueError:
                pass  # Not an IP address, which is good
            
            # Validate against internal/private domains
            forbidden_domains = [
                'localhost', '127.0.0.1', '::1', '0.0.0.0',
                'internal', 'local', 'private', 'intra', 'vpn'
            ]
            
            if any(forbidden in result.hostname.lower() for forbidden in forbidden_domains):
                raise ValueError("Access to internal domains is not allowed")
                
        except Exception as e:
            raise ValueError(f"Invalid URL: {e}")
        
        return url
    
    def is_allowed_url(url):
        try:
            """Check if the URL's domain is in the allowed list."""
            parsed_url = urlparse(url)
            return any(parsed_url.netloc.endswith(domain) for domain in ALLOWED_DOMAINS)
        except Exception as e:
            logger.error(f"Error checking allowed URL: {e}")
            return False

    def resolve_and_validate_dns(url):
        """Resolve DNS and validate it doesn't point to internal IPs"""
        try:
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname
            
            # Resolve DNS
            import socket
            ip = socket.gethostbyname(hostname)
            
            # Check if IP is internal/private
            ip_obj = ipaddress.ip_address(ip)
            
            if ip_obj.is_private or ip_obj.is_loopback or ip_obj.is_link_local:
                raise ValueError(f"IP address {ip} is private/internal")
                
            # Check against known bad IP ranges
            if ip in ['127.0.0.1', '::1', '0.0.0.0']:
                raise ValueError(f"IP address {ip} is not allowed")
                
            return ip
            
        except Exception as e:
            raise ValueError(f"DNS validation failed: {e}")

    # Validate the URL comprehensively
    try:
        url = validate_url(url)
        if not is_allowed_url(url):
            raise ValueError(f"URL domain not allowed: {url}")
        
        # DNS validation for SSRF protection
        resolved_ip = resolve_and_validate_dns(url)
        logger.info(f"Resolved {url} to IP: {resolved_ip}")
        
    except ValueError as e:
        error_message = f"Validation failed: {e}"
        logger.error(error_message)
        return "", error_message
    
    now = datetime.now()
    timestamp = now.strftime('%Y%m%d-%H%M%S') + f'{int(now.microsecond/1000):03d}'
    pod_uid = os.getenv("HOSTNAME", "unknown")
    unique_id = str(uuid.uuid4())[:12]
    new_filename = f"{timestamp}_{pod_uid}_{unique_id}{ext}"
    abs_path = os.path.abspath(os.path.join(target_dir, new_filename))

    proxies = {"https": https_proxy} if https_proxy else None
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0',
            'Accept': 'image/*'  # Explicitly ask for images
        }

        # Add redirect protection
        session = requests.Session()
        session.max_redirects = 3  # Limit redirects
        session.trust_env = False  # Don't use system proxy settings
        
        # NOCA:server_side_request_forgery(SSRF漏洞已经多次修复)
        response = session.get(
            url,
            stream=True,
            headers=headers,
            proxies=proxies,
            timeout=DOWNLOAD_TIMEOUT,
            allow_redirects=True  # But we control max redirects via session
        )
        response.raise_for_status()

        # Validate final URL after redirects
        final_url = response.url
        if not is_allowed_url(final_url):
            raise ValueError(f"Redirected to unauthorized domain: {final_url}")

        # Validate after successful GET
        content_type = response.headers.get('Content-Type', '').lower()
        if not content_type.startswith('image/'):
            return "", "URL does not point to an image."

        content_length = int(response.headers.get('Content-Length', 0))
        if content_length > MAX_IMAGE_SIZE:
            return "", f"File too large: {content_length}, limitation: {MAX_IMAGE_SIZE} bytes currently."

        # Save the image
        with open(abs_path, "wb") as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)

        return abs_path, None
        
    except requests.exceptions.TooManyRedirects:
        return "", "Too many redirects"
    except Exception as e:
        error_message = f"Download image failed: {e}"
        logger.error(error_message)
        return "", error_message

@app.route('/smoke_detect', methods=['POST'])
def smoke_detect():
    begin_ts = time.time()
    logger.info(f"Received smoke detect request: {request}")
    response = {"Code":0, "Msg": "smoke detect success", "Data": {}}
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data provided")
    except Exception as e:
        logger.error(f"Invalid JSON in request: {e}")
        response["Code"] = -1001
        response["Msg"] = "Invalid JSON in request"
        return jsonify(response)
    logger.info(f"Request data: {json.dumps(data, ensure_ascii=False)}")
    
    if 'image_url' not in data:
        logger.error("Missing 'image_urls' in request data")
        response["Code"] = -1002
        response["Msg"] = "Missing 'image_urls' in request data"
        return jsonify(response)
    if isinstance(data['image_url'], list):
        logger.error("'image_url' should not be an array")
        response["Code"] = -1003
        response["Msg"] = "'image_url' should not be array"
        return jsonify(response)

    url = data['image_url']
    test_image_path, error_msg = download_image_with_timestamp(url)
    if error_msg:
        logger.warning(f"Image download failed for {url}: {error_msg}")
        response["Code"] = -1004
        response["Msg"] = "Image download failed"
        return jsonify(response)

        
    if not test_image_path:
        logger.warning(f"Invalid image path for {url}: {test_image_path}")
        response["Code"] = -1005
        response["Msg"] = "Image download failed"
        return jsonify(response)
    logger.info(f"Image downloaded to: {test_image_path}")
    url_list = []
    url_list.append(url)
    try:
        draw_image_type_list = [0]
        session_id = generate_session_id()
        result = smoke_detect_api_call(
        session_id=session_id,
        url_list=url_list,
        draw_image_type_list=draw_image_type_list,
        https_proxy=HTTPS_PROXY
        )
        logger.info(f"smoke detect result: {json.dumps(result, ensure_ascii=False)}")
        # 检查API调用是否成功
        if result.get('errorcode') == 0:
            # 保存图片并获取图片路径列表 draw_image_type_list = [2]时返回
            #image_paths = save_base64_images(result.get('draw_image_list', []),"smoke", session_id)
            image_paths,need_result_img = draw_detection_boxes(test_image_path, result,"smoke", session_id)
            logger.info(f"图片路径列表: {image_paths}")
            if need_result_img:
                response["Data"] = {
                    "result_image": IMAGE_HTTPS_PREFIX + "/" "smoke" + "/"+ session_id + "/" + os.path.basename(image_paths),
                    "detect_msg": "检测到图中存在火焰"
                } 
            else:
                response["Data"] = {
                    "detect_msg": "没有检测到火焰"
                }
        else:
            logger.error(f"API调用失败，错误码: {result.get('errorcode')}, 错误信息: {result.get('errormsg')}")
            response["Code"] = result.get('errorcode')
            response["Msg"] = result.get('errormsg')          
    except Exception as e:
        logger.error(f"smoke detect failed: {traceback.format_exc()}")
        response["Code"] = -1006
        response["Msg"] = "smoke detect failed"
        return jsonify(response)
    logger.info(f"smoke detect result: {json.dumps(result, ensure_ascii=False)}, elapse: {int((time.time() - begin_ts)*1000)} ms")

    return jsonify(response)

def draw_detection_boxes(test_image_path: str, result_json: Dict, service, session_id, output_dir=VISUAL_DIR,) -> Tuple[str, bool]:
    # 创建输出目录
    # 创建以session_id命名的子目录
    session_dir = os.path.join(VISUAL_DIR,service,session_id)
    if not os.path.exists(session_dir):
        os.makedirs(session_dir)
    
    # 读取原始图片
    img = cv2.imread(test_image_path)
    if img is None:
        logger.error(f"无法读取图片: {test_image_path}")
        return None
    
    # 复制图片，不在原图上绘制
    result_img = img.copy()
    
    # 检查API调用是否成功
    if result_json.get('errorcode') != 0:
        error_msg = f"API调用失败: {result_json.get('errormsg')}"
        logger.error(error_msg)
        return None
    
    # 处理检测结果
    result_list = result_json.get('result_list', [])
    if not result_list:
        logger.warning("结果列表为空")
        return None
    
    # 遍历所有结果
    need_result_img = False
    boxes_drawn = 0
    for result in result_list:
        boxes = result.get('boxes', [])
        for box in boxes:
            # 只处理cla=0的框
            if box.get('cla') == 0:
                need_result_img = True
                # 获取坐标
                x = int(box['x'])
                y = int(box['y'])
                w = int(box['w'])
                h = int(box['h'])
                confidence = box.get('confidence', 0)
                
                # 确保坐标在图像范围内
                if x < 0 or y < 0 or w <= 0 or h <= 0:
                    logger.warning(f"无效坐标: x={x}, y={y}, w={w}, h={h}")
                    continue
                
                # 绘制矩形框（红色）
                cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 0, 255), 2)
                
                # 添加置信度文本
                """
                conf_text = f"Conf: {confidence:.2f}"
                cv2.putText(result_img, conf_text, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                """
                
                boxes_drawn += 1
                logger.info(f"绘制框: x={x}, y={y}, w={w}, h={h}, conf={confidence:.2f}")
    
    logger.info(f"成功绘制 {boxes_drawn} 个cla=0的检测框")
    
    # 生成输出文件名
    filename = f"detected_{os.path.basename(test_image_path)}"
    result_image_path = os.path.join(session_dir, filename)
    # 保存结果图片
    cv2.imwrite(result_image_path, result_img)
    logger.info(f"结果图片已保存: {result_image_path}")
    
    return result_image_path, need_result_img

def smoke_detect_api_call(session_id,  
                          url_list: Optional[List[str]] = None,
                          draw_image_type_list: Optional[List[int]] = None,
                          https_proxy=HTTPS_PROXY):
    """
    调用烟雾检测API接口
    
    参数:
    api_url: API的完整URL (例如: http://ip:port/SmokeDetectService/SmokeDetect)
    session_id: 会话ID，用于请求追踪
    url_list: 图片URL列表 (可选)
    image_list: 图片base64编码列表 (可选，优先级高于url_list)
    conf_thresh_list: 置信度阈值列表
    draw_image_type_list: 绘图类型列表 (0: 不画, 2: 框出识别物体)
    """
    
    # 构建请求头
    headers = {
        'Content-Type': 'application/json'
    }
    
    SMOKE_API_URL = "https://ms-gcqvhkkt-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-gcqvhkkt/SmokeDetectService/SmokeDetect"
    # 构建请求体
    payload = {
        'session_id': session_id,
        'url_list': url_list or [],
        'draw_image_type_list': draw_image_type_list or []
    }
    try:
        # 发送POST请求
        proxies = {"https": https_proxy} if https_proxy else None
        response = requests.post(SMOKE_API_URL, headers=headers, data=json.dumps(payload), proxies=proxies)
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析返回的JSON数据
        result = response.json()
        
        # 检查错误码
        if result.get('errorcode') != 0:
            logger.error(f"请求失败，错误码: {result.get('errorcode')}, 错误信息: {result.get('errormsg')}")
            return result
        
        return result
        
    except requests.exceptions.RequestException as e:
        logger.info(f"请求发生异常: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.info(f"JSON解析错误: {e}")
        return None

# 防翻拍
@app.route('/fake_photo', methods=['POST'])
def fake_photo():
    begin_ts = time.time()
    logger.info(f"Received fake photo request: {request}")
    response = {"Code":0, "Msg": "fake photo success", "Data": {}}
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data provided")
    except Exception as e:
        logger.error(f"Invalid JSON in request: {e}")
        response["Code"] = -1001
        response["Msg"] = "Invalid JSON in request"
        return jsonify(response)
    logger.info(f"Request data: {json.dumps(data, ensure_ascii=False)}")
    
    url = data['image_url']
    test_image_path, error_msg = download_image_with_timestamp(url)
    if error_msg:
        logger.warning(f"Image download failed for {url}: {error_msg}")
        response["Code"] = -1002
        response["Msg"] = "Image download failed"
        return jsonify(response)

        
    if not test_image_path:
        logger.warning(f"Invalid image path for {url}: {test_image_path}")
        response["Code"] = -1003
        response["Msg"] = "Image download failed"
        return jsonify(response)
    logger.info(f"Image downloaded to: {test_image_path}")
        
    try:
        session_id = generate_session_id()
        result = fake_photo_api_call(
        session_id=session_id,
        app_id=session_id,
        fake_type=1,  # 1=防翻拍，2=作弊检测
        url=url
        )
    
        logger.info("检测结果:", json.dumps(result, indent=2, ensure_ascii=False))
    
        # 检查结果
        if result.get("status") == 1:
            logger.info(f"检测结果: {result.get('data')}")
            logger.info(f"置信度: {result.get('score')}")
        else:
            logger.error(f"检测失败: {result.get('errormsg')}")
        
        logger.info("检测结果:", json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查结果
        if result.get("status") == 1:
            logger.info(f"检测结果: {result.get('data')}")
            logger.info(f"置信度: {result.get('score')}")
        else:
            logger.error(f"检测失败: {result.get('errormsg')}")

        # 检查API调用是否成功
        if result.get('errorcode') == 0:
            response["Data"] = [
                {
                    "fake_result": result.get('data'), 
                } 
            ] 
        else:
            logger.error(f"API调用失败，错误码: {result.get('errorcode')}, 错误信息: {result.get('errormsg')}")
            response["Code"] = result.get('errorcode')
            response["Msg"] = result.get('errormsg')          
    except Exception as e:
        logger.error(f"fake photo failed: {traceback.format_exc()}")
        response["Code"] = -1006
        response["Msg"] = "fake photo failed"
        return jsonify(response)
    logger.info(f"fake photo result: {json.dumps(result, ensure_ascii=False)}, elapse: {int((time.time() - begin_ts)*1000)} ms")
    return jsonify(response)

def fake_photo_api_call(
    session_id: str,
    app_id: str,
    fake_type: int,
    image: Optional[str] = None,
    url: Optional[str] = None,
    https_proxy=HTTPS_PROXY
) -> Dict[str, Any]:
    
    API_URL = "https://ms-85d9mr7f-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-85d9mr7f/ImageQuality/FakePhoto"
    # 验证参数
    if not image and not url:
        error_msg = "必须提供image或url参数"
        logger.error(error_msg)
        return {
            "status": 0,
            "data": False,
            "score": 0.0,
            "app_id": app_id,
            "session_id": session_id,
            "errorcode": -1,
            "errormsg": error_msg
        }
    
    if image and url:
        logger.warning("同时提供了image和url参数，将优先使用image参数")
    
    # 构建请求体
    payload = {
        "session_id": session_id,
        "app_id": app_id,
        "fake_type": fake_type
    }
    
    # 添加图片数据（优先使用image）
    if image:
        payload["image"] = image
    elif url:
        payload["url"] = url
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    # 记录开始时间
    begin_ts = time.time()
    logger.info(f"fake photo req result: {json.dumps(payload, ensure_ascii=False)}")
    try:
        # 发送POST请求
        proxies = {"https": https_proxy} if https_proxy else None
        response = requests.post(API_URL, headers=headers, data=json.dumps(payload), proxies=proxies)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析响应
        result = response.json()
        
        # 计算耗时
        elapse_ms = int((time.time() - begin_ts) * 1000)
        
        # 记录日志
        logger.info(f"fake photo detect result: {json.dumps(result, ensure_ascii=False)}, elapse: {elapse_ms} ms")
        
        return result
        
    except requests.exceptions.RequestException as e:
        # 网络或HTTP错误
        error_msg = f"请求发生异常: {e}"
        logger.error(error_msg)
        return {
            "status": 0,
            "data": False,
            "score": 0.0,
            "app_id": app_id,
            "session_id": session_id,
            "errorcode": -2,
            "errormsg": error_msg
        }
    except json.JSONDecodeError as e:
        # JSON解析错误
        error_msg = f"JSON解析错误: {e}"
        logger.error(error_msg)
        return {
            "status": 0,
            "data": False,
            "score": 0.0,
            "app_id": app_id,
            "session_id": session_id,
            "errorcode": -3,
            "errormsg": error_msg
        }

# 人体属性
@app.route('/human_obj_attribute', methods=['POST'])
def human_obj_attribute():
    begin_ts = time.time()
    logger.info(f"Received human objattribute request: {request}")
    response = {"Code":0, "Msg": "human objattribute success", "Data": {}}
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data provided")
    except Exception as e:
        logger.error(f"Invalid JSON in request: {e}")
        response["Code"] = -1001
        response["Msg"] = "Invalid JSON in request"
        return jsonify(response)
    logger.info(f"Request data: {json.dumps(data, ensure_ascii=False)}")
    
    if 'image_url' not in data:
        logger.error("Missing 'image_url' in request data")
        response["Code"] = -1002
        response["Msg"] = "Missing 'image_url' in request data"
        return jsonify(response)


    url = data['image_url']
    test_image_path, error_msg = download_image_with_timestamp(url)
    if error_msg:
        logger.warning(f"Image download failed for {url}: {error_msg}")

        
    if not test_image_path or not os.path.isfile(test_image_path):
        logger.warning(f"Invalid image path for {url}: {test_image_path}")
    logger.info(f"Image downloaded to: {test_image_path}")
        

    if not test_image_path:
        logger.error("image downloads failed")
        response["Code"] = -1003
        response["Msg"] = "image downloads failed"
        return jsonify(response)

    try:
        session_id = generate_session_id()
        result = hunman_detect_helmet_api_call(
        session_id=session_id,
        url=url,
        https_proxy=HTTPS_PROXY
        )
        # 检查结果
        if result.get('ErrMsg') != "OK":
            error_msg = f"API调用失败，错误码: {result.get('ErrCode')}, 错误信息: {result.get('ErrMsg')}"
            logger.error(error_msg)
            response["Code"] = result.get('ErrCode')
            response["Msg"] = result.get('ErrMsg')
            return jsonify(response)
        
        # 绘制安全帽检测结果
        result_path, no_helmet_count = draw_helmet_detection(test_image_path, result)
        logger.info(f"结果图片已保存到: {result_path}")
        if no_helmet_count > 0:
            response["Data"] = {
                "result_image": IMAGE_HTTPS_PREFIX + "/" + os.path.basename(result_path),
                "detect_msg": f"检测到{no_helmet_count}个未戴安全帽",
            } 
        else:
            response["Data"] = {
                "detect_msg": f"未检测到安全帽",
            }     
    except Exception as e:
        logger.error(f"human objattribute failed: {traceback.format_exc()}")
        response["Code"] = -1005
        response["Msg"] = "human objattribute failed"
        return jsonify(response)
    logger.info(f"human objattribute result: {json.dumps(result, ensure_ascii=False)}, elapse: {int((time.time() - begin_ts)*1000)} ms")
    return jsonify(response)

def draw_helmet_detection(image_path: str, result_json: Dict, output_dir=VISUAL_DIR)  -> Tuple[str, int]:
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取原始图片
    img = cv2.imread(image_path)
    if img is None:
        logger.error(f"无法读取图片: {image_path}")
        return None
    
    # 复制图片，不在原图上绘制
    result_img = img.copy()
    
    # 检查API调用是否成功
    if result_json.get('ErrMsg') != "OK":
        error_msg = f"API调用失败: {result_json.get('ErrMsg')}"
        logger.error(error_msg)
        return None
    
    # 处理检测结果
    no_helmet_count = 0
    obj_list = result_json.get('ObjList', [])
    for i, obj in enumerate(obj_list):
        # 获取矩形框坐标
        rectf = obj.get('Rectf', {})
        x = int(rectf.get('X', 0))
        y = int(rectf.get('Y', 0))
        width = int(rectf.get('Width', 0))
        height = int(rectf.get('Height', 0))
        
        # 获取安全帽属性
        attribute_map = obj.get('AttributeMap', {})
        helmet_info = attribute_map.get('helmet', {})
        helmet_label = helmet_info.get('Label', -1)
        helmet_confidence = helmet_info.get('Confidence', 0)
        
        # 确定颜色和标签文本
        """
        if helmet_label == 1:  # 有安全帽
            color = (0, 255, 0)  # 绿色
            label_text = f"有安全帽 ({helmet_confidence:.2f})"
        else:  # 无安全帽或不确定 (0或2)
            color = (0, 0, 255)  # 红色
            label_text = f"helmet ({helmet_confidence:.2f})"
        """
        if helmet_label != 1: #no helmet
            color = (0, 0, 0)  # 红色
            label_text = f"no helmet ({helmet_confidence:.2f})"
        else:
            continue
        
        # 绘制矩形框
        box_color = (0, 255, 0)
        cv2.rectangle(result_img, (x, y), (x + width, y + height), box_color, 2)
        no_helmet_count = no_helmet_count + 1
        # 添加标签文本
        """
        cv2.putText(result_img, label_text, (x, y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        """
        
        logger.info(f"对象 {i+1}: {label_text}, 坐标: x={x}, y={y}, width={width}, height={height}")
    
    # 生成输出文件名
    filename = f"helmet_detection_{os.path.basename(image_path)}"
    result_image_path = os.path.join(output_dir, filename)
    
    # 保存结果图片
    cv2.imwrite(result_image_path, result_img)
    logger.info(f"结果图片已保存: {result_image_path}")
    
    return result_image_path,no_helmet_count

def hunman_detect_helmet_api_call(session_id, url,https_proxy=HTTPS_PROXY):
    """
    调用安全帽检测API并在图片上绘制检测框
    
    参数:
    session_id: 会话ID，用于追踪请求
    url: 图片下载地址
    
    返回:
    result_img_path: 处理后的图片保存路径
    """
    API_URL = " https://ms-2vpzc55c-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-2vpzc55c/Attribute/ObjAttribute"
    
    # 记录开始时间
    begin_ts = time.time()
    
    try:
        # 构建请求体
        payload = {
            "SessionID": session_id,
            "ImageUrl": url,
            "Params": json.dumps({"return_body_img": False})
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        # 发送POST请求
        proxies = {"https": https_proxy} if https_proxy else None
        response = requests.post(API_URL, headers=headers, data=json.dumps(payload),proxies=proxies)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析响应
        result = response.json()
        
        # 计算耗时
        elapse_ms = int((time.time() - begin_ts) * 1000)
        
        # 记录日志
        logger.info(f"helmet detect result: {json.dumps(result, ensure_ascii=False)}, elapse: {elapse_ms} ms")

    except requests.exceptions.RequestException as e:
        error_msg = f"请求发生异常: {e}"
        logger.error(error_msg)
        return None
    except json.JSONDecodeError as e:
        error_msg = f"JSON解析错误: {e}"
        logger.error(error_msg)
        return None
    return result

# 车牌识别
@app.route('/plate_ocr', methods=['POST'])
def plate_ocr():
    begin_ts = time.time()
    logger.info(f"Received plate ocr request: {request}")
    response = {"Code":0, "Msg": "plate ocr success", "Data": {}}
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data provided")
    except Exception as e:
        logger.error(f"Invalid JSON in request: {e}")
        response["Code"] = -1001
        response["Msg"] = "Invalid JSON in request"
        return jsonify(response)
    logger.info(f"Request data: {json.dumps(data, ensure_ascii=False)}")
    
    if 'image_url' not in data:
        logger.error("Missing 'image_url' in request data")
        response["Code"] = -1002
        response["Msg"] = "Missing 'image_url' in request data"
        return jsonify(response)

    url = data['image_url']
    test_image_path, error_msg = download_image_with_timestamp(url)
    if error_msg:
        logger.warning(f"Image download failed for {url}: {error_msg}")
        
    if not test_image_path or not os.path.isfile(test_image_path):
        logger.warning(f"Invalid image path for {url}: {test_image_path}")
    logger.info(f"Image downloaded to: {test_image_path}")
        
    if not test_image_path:
        logger.error("image downloads failed")
        response["Code"] = -1003
        response["Msg"] = "image downloads failed"
        return jsonify(response)

    try:
        # 车牌抠图
        session_id = generate_session_id()
        iamges_base64 = image_to_base64(test_image_path)
        result = recognize_vehicle_and_save_plate(
        image_base64=iamges_base64,
        session_id=session_id,
        https_proxy=HTTPS_PROXY
        )

        # 处理识别结果
        results = result.get('results', [])
        logger.info(f"results: {json.dumps(results, ensure_ascii=False)}")
        if not results:
            logger.error("未检测出车辆")
            response["Code"] = -1004
            response["Msg"] = "未检测出车辆"
            return jsonify(response)
    
        original_image_path = test_image_path
        # 提取坐标信息
        coordinates = extract_coordinates(results)
        logger.info(f"提取到 {len(coordinates)} 个坐标区域")
        # 裁剪图像并获取 Base64
        crops = crop_image(original_image_path, coordinates)
        if not crops:
            error_msg = "未能成功裁剪出车辆图像"
            logger.error(error_msg)
            response["Code"] = -1
            response["Msg"] = error_msg
            return response
        # 处理每个车辆
        plate_results = []
        for crop in crops:
            try:
                # 调用车牌OCR API
                PLATE_OCR_URL="https://ms-9s6dbsth-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-9s6dbsth/youtu/ocrapi/plateocr"
                plate_result = plate_ocr_api_call(
                    image_base64=crop['base64_data'],
                    app_id="10000",
                    session_id=session_id,
                    api_url=PLATE_OCR_URL,
                    https_proxy=HTTPS_PROXY
                )
                
                # 检查OCR结果
                if plate_result.get('errorcode') != 0:
                    logger.error(f"车牌识别失败: {plate_result.get('errormsg')}")
                    continue
                
                # 处理OCR结果
                items = plate_result.get('items', [])
                if not items:
                    logger.warning("未识别到车牌")
                    continue
                
                # 获取第一个车牌结果
                plate_info = items[0]
                
                # 获取车牌在车辆图像中的坐标
                itemcoord = plate_info.get('itemcoord', {})
                px = itemcoord.get('x', 0)
                py = itemcoord.get('y', 0)
                pw = itemcoord.get('width', 0)
                ph = itemcoord.get('height', 0)
                
                # 转换为原图绝对坐标
                vx = crop['coordinates']['x']
                vy = crop['coordinates']['y']
                absolute_bbox = (vx + px, vy + py, pw, ph)
                
                # 添加绝对坐标到结果
                plate_info['absolute_bbox'] = absolute_bbox
                
                # 添加到结果列表
                plate_results.append(plate_info)
                
                logger.info(f"车辆 {crop['crop_index']} 车牌识别结果: {plate_info.get('itemstring')}")
                
            except Exception as e:
                logger.error(f"处理车辆 {crop['crop_index']} 时出错: {str(e)}")
                response["Code"] = -1004
                response["Msg"] = "plate_ocr failed"
                return jsonify(response)
        
        # 在原图上绘制所有结果
        result_image_path, plate_count = draw_vehicle_and_plate_on_image(
            test_image_path, 
            crops, 
            plate_results,
            VISUAL_DIR
        )
        
        # 构建响应数据
        response["Data"]["plate_data"] = []
        for i, crop in enumerate(crops):
            plate_info = plate_results[i] if i < len(plate_results) else {}
            if not plate_info.get('itemstring'):
                continue
            # 添加到plate_data列表
            response["Data"]["plate_data"].append({
                "plate_info": {
                    "plate_number": plate_info.get('itemstring', ''),
                    "plate_color": plate_info.get('color', ''),
                    "confidence": plate_info.get('itemconf', 0)
                }
            }) 

        if plate_count > 0:
            # 设置消息
            response["Data"]["plate_msg"] = f"检测到{plate_count}个车牌"
            response["Data"]["result_image"] = IMAGE_HTTPS_PREFIX + "/" + os.path.basename(result_image_path)
        else:
            rresponse = {"Code":0, "Msg": "未检测到车牌", "Data": {}}
            return jsonify(response)         
    except Exception as e:
        logger.error(f"plate_ocr failed: {traceback.format_exc()}")
        response["Code"] = -1005
        response["Msg"] = "plate_ocr failed"
        return jsonify(response)
    logger.info(f"plate ocr elapse: {int((time.time() - begin_ts)*1000)} ms")
    return jsonify(response)

def plate_ocr_api_call(image_base64, app_id, session_id, api_url,https_proxy=HTTPS_PROXY):
    """
    调用车牌识别API
    
    参数:
    image_base64: 使用base64编码的二进制图片数据
    app_id: 调用服务的唯一ID
    session_id: 会话ID (可选，如未提供将自动生成)
    api_base_url: API基础地址
    
    返回:
    包含识别结果的字典
    """
    
    # 构建请求头
    headers = {
        'Content-Type': 'application/json',
    }
    
    # 构建请求体
    payload = {
        "app_id": app_id,
        "image": image_base64,
        "session_id": session_id
    }
    
    # 记录开始时间
    begin_ts = time.time()
    try:
        # 发送POST请求

        proxies = {"https": https_proxy} if https_proxy else None
        response = requests.post(api_url, headers=headers, data=json.dumps(payload), proxies=proxies)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析响应
        result = response.json()
        
        # 计算耗时
        elapse_ms = int((time.time() - begin_ts) * 1000)
        
        # 记录日志
        logger.info(f"plate ocr result: {json.dumps(result, ensure_ascii=False)}, elapse: {elapse_ms} ms")
        
        # 检查API调用是否成功
        if result.get('errorcode') != 0:
            error_msg = f"API调用失败，错误码: {result.get('errorcode')}, 错误信息: {result.get('errormsg')}"
            logger.error(error_msg)
            return {
                "errorcode": result.get('errorcode'),
                "errormsg": result.get('errormsg'),
                "session_id": session_id
            }
        
        # 返回成功结果
        return result
        
    except requests.exceptions.RequestException as e:
        error_msg = f"请求发生异常: {e}"
        logger.error(error_msg)
        return {
            "errorcode": -1,
            "errormsg": error_msg,
            "session_id": session_id
        }
    except json.JSONDecodeError as e:
        error_msg = f"JSON解析错误: {e}"
        logger.error(error_msg)
        return {
            "errorcode": -2,
            "errormsg": error_msg,
            "session_id": session_id
        }

def extract_coordinates(results: List[Dict]) -> List[Tuple[Tuple[int, int, int, int], Dict]]:
    coordinates = []
    for obj in results:
        if "coord" not in obj:
            continue
            
        try:
            coord = obj["coord"]
            # 将浮点数坐标转换为整数
            x = int(round(coord["x"]))
            y = int(round(coord["y"]))
            width = int(round(coord["width"]))
            height = int(round(coord["height"]))
            
            # 返回边界框和元数据
            coordinates.append(
                ((x, y, width, height), 
                 {"type": obj.get("type", ""),
                  "color": obj.get("color", ""),
                  "brand": obj.get("brand", "")})
            )
        except (KeyError, TypeError) as e:
            logger(f"无法解析坐标: {e}")
    
    return coordinates

def crop_image(image_path: str, coordinates: List[Tuple[Tuple[int, int, int, int], Dict]]) -> List[Dict]:
    # 读取原图
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在: {image_path}")
    
    # 使用 OpenCV 读取图像
    image = cv2.imread(image_path)
    if image is None:
        # 如果 OpenCV 读取失败，尝试使用 PIL
        try:
            pil_image = Image.open(image_path)
            image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        except Exception as e:
            raise RuntimeError(f"无法读取图片: {str(e)}")
    
    results = []
    
    for i, ((x, y, w, h), meta) in enumerate(coordinates):
        try:
            # 确保坐标在图像范围内
            y1 = max(0, y)
            x1 = max(0, x)
            y2 = min(image.shape[0], y1 + h)
            x2 = min(image.shape[1], x1 + w)
            
            # 如果坐标完全在图像外，跳过
            if x1 >= x2 or y1 >= y2:
                print(f"坐标超出图像范围: {x}, {y}, {w}, {h}")
                continue
                
            # 裁剪图像
            cropped_img = image[y1:y2, x1:x2]
            
            # 生成 Base64 编码
            is_success, buffer = cv2.imencode(".jpg", cropped_img)
            if not is_success:
                raise RuntimeError("图像编码失败")
            
            img_base64 = base64.b64encode(buffer).decode("utf-8")
            
            # 保存到文件系统（可选）
            filename = f"crop_{os.path.splitext(os.path.basename(image_path))[0]}_{i+1}.jpg"
            cv2.imwrite(filename, cropped_img)
            
            # 添加结果
            results.append({
                "crop_index": i + 1,
                "coordinates": {"x": x1, "y": y1, "width": w, "height": h},
                "file_path": filename,
                "base64_data": img_base64,
                "metadata": meta
            })
            
        except Exception as e:
            logger.error(f"处理裁剪图像时出错: {str(e)}")
    
    return results

def draw_vehicle_and_plate_on_image(test_image_path: str, 
                                   vehicles: List[Dict], 
                                   plate_results: List[Dict],
                                   output_dir: str = VISUAL_DIR) -> Tuple[str, int]:
    """
    在图片上绘制车辆框、车牌框和车辆序号
    只绘制成功识别到车牌的车辆
    
    参数:
    test_image_path: 原始图片路径
    vehicles: 车辆信息列表
    plate_results: 车牌识别结果列表
    output_dir: 输出目录
    
    返回:
    result_image_path: 处理后的图片保存路径
    plate_count: 成功识别的车牌数量
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取原始图片
    img = cv2.imread(test_image_path)
    if img is None:
        logger.error(f"无法读取图片: {test_image_path}")
        return None, 0
    
    # 转换为PIL图像
    pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_img)
    
    # 使用大号字体显示车辆序号
    font_size = 36
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # 回退到默认字体
        font = ImageFont.load_default()
    
    # 计算成功识别的车牌数量
    plate_count = len(plate_results)
    
    # 绘制有车牌识别结果的车辆框和序号
    for i, plate in enumerate(plate_results):
        # 获取车辆索引（假设plate_results中的顺序与vehicles中成功识别的车辆顺序一致）
        vehicle_index = i
        
        # 确保不超出vehicles范围
        if vehicle_index >= len(vehicles):
            continue
            
        # 获取车辆信息
        vehicle = vehicles[vehicle_index]
        vx = vehicle['coordinates']['x']
        vy = vehicle['coordinates']['y']
        vw = vehicle['coordinates']['width']
        vh = vehicle['coordinates']['height']
        
        # 生成随机颜色
        r = random.randint(100, 255)
        g = random.randint(100, 255)
        b = random.randint(0, 255)
        color = (r, g, b)
        
        # 绘制车辆框
        draw.rectangle([vx, vy, vx + vw, vy + vh], outline=color, width=2)
        
        # 在车辆框左上角添加序号（白色背景，红色数字）
        vehicle_num = str(i + 1)
        
        # 获取文本尺寸
        try:
            # 新版本Pillow使用getbbox
            bbox = draw.textbbox((0, 0), vehicle_num, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        except AttributeError:
            # 如果textbbox不存在，使用估计值
            text_width = len(vehicle_num) * font_size
            text_height = font_size
        
        # 创建背景框
        bg_x1 = vx
        bg_y1 = vy
        bg_x2 = bg_x1 + text_width + 10
        bg_y2 = bg_y1 + text_height + 10
        draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill="white")
        
        # 添加序号
        draw.text((bg_x1 + 5, bg_y1 + 5), vehicle_num, fill="red", font=font)
        
        # 绘制车牌框（绿色）
        if 'absolute_bbox' in plate:
            px, py, pw, ph = plate['absolute_bbox']
            draw.rectangle([px, py, px + pw, py + ph], outline="green", width=2)
    
    # 转换回OpenCV格式
    result_img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    # 生成输出文件名
    filename = f"result_{os.path.basename(test_image_path)}"
    result_image_path = os.path.join(output_dir, filename)
    
    # 保存结果图片
    cv2.imwrite(result_image_path, result_img)
    logger.info(f"结果图片已保存: {result_image_path}")
    
    return result_image_path, plate_count

def draw_vehicle_and_plate_on_image_v1(test_image_path: str, 
                                   vehicles: List[Dict], 
                                   plate_results: List[Dict],
                                   output_dir: str = VISUAL_DIR) -> str:
    """
    在图片上绘制车辆框、车牌框和车辆序号
    
    参数:
    test_image_path: 原始图片路径
    vehicles: 车辆信息列表
    plate_results: 车牌识别结果列表
    output_dir: 输出目录
    
    返回:
    处理后的图片保存路径
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取原始图片
    img = cv2.imread(test_image_path)
    if img is None:
        logger.error(f"无法读取图片: {test_image_path}")
        return None
    
    # 转换为PIL图像
    pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_img)
    
    # 使用大号字体显示车辆序号
    font_size = 36
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # 回退到默认字体
        font = ImageFont.load_default()
    
    # 绘制所有车辆框和序号
    for i, vehicle in enumerate(vehicles):
        # 车辆坐标
        vx = vehicle['coordinates']['x']
        vy = vehicle['coordinates']['y']
        vw = vehicle['coordinates']['width']
        vh = vehicle['coordinates']['height']
        
        
        # 生成随机颜色
        r = random.randint(100, 255)
        g = random.randint(100, 255)
        b = random.randint(0, 255)
        color = (r, g, b)
        # 绘制车辆框
        draw.rectangle([vx, vy, vx + vw, vy + vh],  outline=color, width=2)
        
        # 在车辆框左上角添加序号（白色背景，红色数字）
        vehicle_num = str(i + 1)
        
        # 获取文本尺寸（兼容Pillow 10.0.0+）
        try:
            # 新版本Pillow使用getbbox
            bbox = draw.textbbox((0, 0), vehicle_num, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        except AttributeError:
            # 如果textbbox不存在，使用旧方法（但可能不可用）
            try:
                text_width, text_height = font.getsize(vehicle_num)
            except:
                # 如果都不行，使用估计值
                text_width = len(vehicle_num) * font_size
                text_height = font_size
        
        # 创建背景框
        bg_x1 = vx
        bg_y1 = vy
        bg_x2 = bg_x1 + text_width + 10
        bg_y2 = bg_y1 + text_height + 10
        draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill="white")
        
        # 添加序号
        draw.text((bg_x1 + 5, bg_y1 + 5), vehicle_num, fill="red", font=font)
    
    # 绘制所有车牌框
    for plate in plate_results:
        if 'absolute_bbox' in plate:
            # 车牌绝对坐标
            px, py, pw, ph = plate['absolute_bbox']
            
            # 绘制车牌框（绿色）
            draw.rectangle([px, py, px + pw, py + ph], outline="green", width=2)
    
    # 转换回OpenCV格式
    result_img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    # 生成输出文件名
    filename = f"result_{os.path.basename(test_image_path)}"
    result_image_path = os.path.join(output_dir, filename)
    
    # 保存结果图片
    cv2.imwrite(result_image_path, result_img)
    logger.info(f"结果图片已保存: {result_image_path}")
    
    return result_image_path

def recognize_vehicle_and_save_plate(image_base64, session_id, https_proxy=HTTPS_PROXY):
    """
    调用车辆识别API并保存车牌抠图
    
    参数:
    image_base64: 使用base64编码的二进制图片数据
    api_url: API地址 http://ip:port/trafficapi/RecVehicle
    app_id: 调用服务的唯一ID (可选)
    session_id: 会话ID (可选，如未提供将自动生成)
    
    返回:
    包含识别结果和车牌图片路径的字典
    """
    # 构建请求头 'Host': IMAGE_HTTPS_PREFIX, #'localhost',
    headers = {
        'Content-Type': 'application/json'
    }
    
    # 构建请求体
    payload = {
        "session_id": session_id,
        "image": image_base64
    }
    
    # 计算Content-Length
    json_payload = json.dumps(payload)
    content_length = len(json_payload.encode('utf-8'))
    headers['Content-Length'] = str(content_length)
    
    try:
        # 发送POST请求
        api_url = "https://ms-qgh6pldh-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-qgh6pldh/trafficapi/RecVehicle"
        proxies = {"https": https_proxy} if https_proxy else None
        response = requests.post(api_url, headers=headers, data=json_payload,proxies=proxies)
        response.raise_for_status()  # 检查HTTP错误
        
        # 解析响应
        result = response.json()
        logger.info(f"plate detect 1 result: {json.dumps(result, ensure_ascii=False)}")
        # 检查API调用是否成功
        if result.get('errorcode') != 0:
            error_msg = f"API调用失败，错误码: {result.get('errorcode')}, 错误信息: {result.get('errormsg')}"
            logger.error(error_msg)
            return {
                "errorcode": result.get('errorcode'),
                "errormsg": result.get('errormsg'),
                "plate_image_base64": None
            }
        
        return result
        
    except requests.exceptions.RequestException as e:
        error_msg = f"请求发生异常: {e}"
        logger.error(error_msg)
        return {
            "errorcode": -1,
            "errormsg": error_msg,
            "plate_url": None
        }
    except json.JSONDecodeError as e:
        error_msg = f"JSON解析错误: {e}"
        logger.error(error_msg)
        return {
            "errorcode": -2,
            "errormsg": error_msg,
            "plate_url": None
        }

# zhipu big model
@app.route('/zhipu_ai', methods=['POST'])
def zhipu_ai():
    begin_ts = time.time()
    logger.info(f"Received ai request: {request}")
    response = {"Code":0, "Msg": "ai success", "Data": {}}
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data provided")
    except Exception as e:
        logger.error(f"Invalid JSON in request: {e}")
        response["Code"] = -1001
        response["Msg"] = "Invalid JSON in request"
        return jsonify(response)
    logger.info(f"Request data: {json.dumps(data, ensure_ascii=False)}")
    
    if 'image_url' not in data:
        logger.error("Missing 'image_url' in request data")
        response["Code"] = -1002
        response["Msg"] = "Missing 'image_url' in request data"
        return jsonify(response)
    
    if 'prompts' not in data:
        logger.error("Missing 'prompts' in request data")
        response["Code"] = -1002
        response["Msg"] = "Missing 'prompts' in request data"
        return jsonify(response)

    url = data['image_url']
    test_image_path, error_msg = download_image_with_timestamp(url)
    if error_msg:
        logger.warning(f"Image download failed for {url}: {error_msg}")
        
    if not test_image_path or not os.path.isfile(test_image_path):
        logger.warning(f"Invalid image path for {url}: {test_image_path}")

    if not test_image_path:
        logger.error("image downloads failed")
        response["Code"] = -1003
        response["Msg"] = "image downloads failed"
        return jsonify(response)
    logger.info(f"Image downloaded to: {test_image_path}")

    text = data['prompts']
    try:
        from zai import ZhipuAiClient
        with open(test_image_path, "rb") as img_file:
            img_base = base64.b64encode(img_file.read()).decode("utf-8")
            
        client = ZhipuAiClient(api_key="a26a34abcb654e4aad5279b158e2d500.BOEMXw83EFP7J2fy",http_client=httpx.Client(proxy=HTTPS_PROXY))
        ai_response = client.chat.completions.create(
        model="glm-4.5v",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": img_base
                        }
                    },
                    {
                        "type": "text",
                        "text": text
                    }
                ]
            }
        ],
        thinking={
            "type": "enabled"
        }
        )
    
        response["Data"] = [
        {
            "messages": ai_response.choices[0].message.content, 
        } 
        ] 

               
    except Exception as e:
        logger.error(f"ai failed: {traceback.format_exc()}")
        response["Code"] = -1004
        response["Msg"] = "ai failed"
        return jsonify(response)
    logger.info(f"zhipu ai result: {json.dumps(response, ensure_ascii=False)}, elapse: {int((time.time() - begin_ts)*1000)} ms")
    return jsonify(response)

@app.route('/get_timestamp_with_ms', methods=['POST'])
def get_timestamp_with_ms():
    now = datetime.now()
    timestamp = now.strftime('%Y%m%d-%H%M%S.') + f'{int(now.microsecond/1000):03d}'
    return jsonify({'timestamp': timestamp})

@app.route('/<path:filename>')
def serve_image(filename):
    # Security: Prevent directory traversal attacks
    if '..' in filename or filename.startswith('/'):
        abort(404)
    # Only serve files that exist
    if not os.path.isfile(os.path.join(VISUAL_DIR, filename)):
        abort(404)
    return send_from_directory(VISUAL_DIR, filename)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=PORT)