import logging
import os
import sys

LOGGER_NAME = 'my_server'

from flask import g, has_request_context

class SessionIDFilter(logging.Filter):
    def filter(self, record):
        if has_request_context():  # Only try to access `g` during requests
            record.session_id = getattr(g, 'session_id', 'no-session')
        else:
            record.session_id = 'no-context'  # Default for non-request logs
        return True

def get_logger():
    logger = logging.getLogger(LOGGER_NAME)
    if not logger.hasHandlers():
        logger.setLevel(logging.INFO)

        # Log to stdout (k8s captures this automatically)
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] [Pod: %(hostname)s] [SID: %(session_id)s] %(message)s'
        )
        handler.setFormatter(formatter)

        # Add the session ID filter
        logger.addFilter(SessionIDFilter())
        logger.addHandler(handler)
        logger.propagate = False  # Prevent double logging if root logger is configured

        # Optional: Inject hostname (Pod name) into log records
        old_factory = logging.getLogRecordFactory()
        def record_factory(*args, **kwargs):
            record = old_factory(*args, **kwargs)
            record.hostname = os.getenv("HOSTNAME", "unknown")
            return record
        logging.setLogRecordFactory(record_factory)
        
    return logger

if __name__ == '__main__':
    # Test logger configuration
    logger = get_logger()
    logger.info("Logger initialized successfully.")