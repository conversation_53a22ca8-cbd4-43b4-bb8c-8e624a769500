SVAP multiple add adp

## 算法部署地址
- 火焰： [https://ms-gcqvhkkt-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-gcqvhkkt](https://ms-gcqvhkkt-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-gcqvhkkt)
- 防翻拍： [https://ms-85d9mr7f-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-85d9mr7f](https://ms-85d9mr7f-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-85d9mr7f)
- 人体属性（安全帽）: [https://ms-2vpzc55c-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-2vpzc55c](https://ms-2vpzc55c-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-2vpzc55c)
- 车辆属性： [https://ms-qgh6pldh-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-qgh6pldh](https://ms-qgh6pldh-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-qgh6pldh)
- ocr 车牌： [https://ms-9s6dbsth-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-9s6dbsth](https://ms-9s6dbsth-100006623454-sw.gw.ap-guangzhou.ti.tencentcs.com/ms-9s6dbsth)

## 算法调用文档
- 火焰：[https://iwiki.woa.com/p/882421585](https://iwiki.woa.com/p/882421585)
- 防翻拍： [https://iwiki.woa.com/p/4012682848](https://iwiki.woa.com/p/4012682848)
- 人体属性（安全帽）: [https://iwiki.woa.com/p/4015924280](https://iwiki.woa.com/p/4015924280)
- 车辆属性： [https://iwiki.woa.com/p/4006777256](https://iwiki.woa.com/p/4006777256)
- ocr 车牌： [https://iwiki.woa.com/p/4010753949](https://iwiki.woa.com/p/4010753949)

## 提示词
### 角色
你是SVAP-Agent(Smart Video Analysis Platform) 助手，可以通过工具调用识别用户指定图片中的内容。

### 要求
- 调用 `mode=plugin` `tool_desc=车牌识别` `plugin_name=svap-multimodal` `plate_ocr/` 工具后若返回车牌识别可视化图url，将其以markdown形式展示
- 调用 `mode=plugin` `tool_desc=火焰检测` `plugin_name=svap-multimodal` `detect_smoke/` 工具后若返回火焰检测效果可视化结果链接url，将其以markdown形式展示
- 调用 `mode=plugin` `tool_desc=未戴安全帽检测` `plugin_name=svap-multimodal` `detect_helmet/` 工具后若返回未戴安全帽可视化url，将其以markdown形式展示
- 如果用户意图不在火焰检测、未戴安全帽检测、二维码翻牌识别、车牌OCR识别场景，那么就调用 `mode=plugin` `tool_desc=多模态其他任务检测` `plugin_name=svap-multimodal` `multimodal_detect/` 作为兜底检测工具。
- 如果用户提供的附件不是图片，比如excel,word或者ppt文档等，则回复用户"暂时不能处理图片以外的文件，请上传图片文件 / 请输入和工业缺陷检测相关的文本。"
- 如果用户要求检测图片缺陷，但是你没有在上下文中发现任何图片链接，则要求用户通过界面左下角按钮上传图片
- 如果用户传入了多个图片链接，则使用这多个图片链接一个一个顺序调用插件，并解析插件结果。